<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Fire_H7</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060960::V5.06 update 7 (build 960)::.\ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32H743XIHx</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32H7xx_DFP.2.6.0</PackID>
          <PackURL>https://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x00020000) IRAM2(0x24000000,0x00080000) IROM(0x08000000,0x00100000) IROM2(0x08100000,0x00100000) XRAM(0x30000000,0x00048000) XRAM2(0x38000000,0x00010000) CPUTYPE("Cortex-M7") FPU3(DFPU) CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC8000 -FN1 -FF0STM32H7x_2048 -********** -********* -FP0($$Device:STM32H743XIHx$CMSIS\Flash\STM32H7x_2048.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32H743XIHx$Drivers\CMSIS\Device\ST\STM32H7xx\Include\stm32h7xx.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32H743XIHx$CMSIS\SVD\STM32H743x.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>..\..\Output\</OutputDirectory>
          <OutputName>Fire_H7</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>0</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>0</BrowseInformation>
          <ListingPath>..\..\Listing\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP -MPU</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM7</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> -MPU</TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM7</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M7"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>1</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>3</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <RvdsCdeCp>0</RvdsCdeCp>
            <hadIRAM2>1</hadIRAM2>
            <hadIROM2>1</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>4</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>1</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </IROM>
              <XRAM>
                <Type>1</Type>
                <StartAddress>0x30000000</StartAddress>
                <Size>0x48000</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x8100000</StartAddress>
                <Size>0x100000</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x30000000</StartAddress>
                <Size>0x48000</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x38000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x20000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x24000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>2</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>0</uGnu>
            <useXO>0</useXO>
            <v6Lang>3</v6Lang>
            <v6LangP>3</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>USE_HAL_DRIVER,STM32H743xx,PID_ASSISTANT_EN</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\Libraries\CMSIS\Include;..\..\Libraries\CMSIS\Device\ST\STM32H7xx\Include;..\..\Libraries\STM32H7xx_HAL_Driver\Inc;..\..\User</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <ClangAsOpt>4</ClangAsOpt>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>STARTUP</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32h743xx.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\Libraries\CMSIS\Device\ST\STM32H7xx\Source\Templates\arm\startup_stm32h743xx.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>CMSIS</GroupName>
          <Files>
            <File>
              <FileName>system_stm32h7xx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\CMSIS\Device\ST\STM32H7xx\Source\Templates\system_stm32h7xx.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>STM32H7xx_HAL_Driver</GroupName>
          <Files>
            <File>
              <FileName>stm32h7xx_hal.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_adc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_adc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cec.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_comp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_cortex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_crc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_crc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_cryp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cryp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_cryp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cryp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dac_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dac_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dcmi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dcmi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dfsdm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dfsdm.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dma_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_dsi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dsi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_eth.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_eth.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_eth_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_eth_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_fdcan.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_fdcan.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_flash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hash.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_hash_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hash_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_hcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hcd.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_hrtim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hrtim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_hsem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_i2c_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_i2s.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2s.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_i2s_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2s_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_irda.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_irda.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_jpeg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_jpeg.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_lptim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_ltdc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_ltdc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_ltdc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_ltdc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_mdios.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdios.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_mdma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_mmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_mmc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mmc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_msp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_msp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_nand.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_nand.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_nor.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_nor.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_opamp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_opamp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_opamp_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_opamp_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_pcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pcd.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_pcd_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pcd_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_pwr_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_qspi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_qspi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_ramecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_ramecc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_rcc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_rtc_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rtc_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sai.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sai.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sai_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sai_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sd.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sd_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sd_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sdram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sdram.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_smartcard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_smartcard.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_smartcard_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_smartcard_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_smbus.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_smbus.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_spdifrx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_spdifrx.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_spi_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_spi_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_sram.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_sram.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_swpmi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_swpmi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_tim_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_tim_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_uart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_usart_ex.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_usart_ex.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_hal_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_bdma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_bdma.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_comp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_comp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_delayblock.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_delayblock.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_dma2d.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_dma2d.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_fmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_fmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_hrtim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_hrtim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_lptim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_lptim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_lpuart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_lpuart.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_mdma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_mdma.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_opamp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_opamp.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_rng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_rng.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_sdmmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_sdmmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_swpmi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_swpmi.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_ll_usb.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\Libraries\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_usb.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>USER</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\main.c</FilePath>
            </File>
            <File>
              <FileName>stm32h7xx_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\stm32h7xx_it.c</FilePath>
            </File>
            <File>
              <FileName>bsp_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\led\bsp_led.c</FilePath>
            </File>
            <File>
              <FileName>core_delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\delay\core_delay.c</FilePath>
            </File>
            <File>
              <FileName>bsp_debug_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\usart\bsp_debug_usart.c</FilePath>
            </File>
            <File>
              <FileName>bsp_motor_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\tim\bsp_motor_tim.c</FilePath>
            </File>
            <File>
              <FileName>bsp_bldcm_control.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\bldcm_control\bsp_bldcm_control.c</FilePath>
            </File>
            <File>
              <FileName>bsp_key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\key\bsp_key.c</FilePath>
            </File>
            <File>
              <FileName>bsp_pid.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\pid\bsp_pid.c</FilePath>
            </File>
            <File>
              <FileName>protocol.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\protocol\protocol.c</FilePath>
            </File>
            <File>
              <FileName>bsp_basic_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\User\tim\bsp_basic_tim.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>DOC</GroupName>
          <Files>
            <File>
              <FileName>必读说明.txt</FileName>
              <FileType>5</FileType>
              <FilePath>..\..\Doc\必读说明.txt</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

</Project>
