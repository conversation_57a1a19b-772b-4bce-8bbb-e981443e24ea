{"version": "0.2.0", "configurations": [{"name": "C/C++ Runner: Debug Session", "type": "cppdbg", "request": "launch", "args": [], "stopAtEntry": false, "externalConsole": true, "cwd": "e:/kx_motor_control/ebf_motor_tutorial_code_stm32h743_fanxing_20250510/improve_part/直流无刷电机-速度环控制-增量式PID/User/bldcm_control", "program": "e:/kx_motor_control/ebf_motor_tutorial_code_stm32h743_fanxing_20250510/improve_part/直流无刷电机-速度环控制-增量式PID/User/bldcm_control/build/Debug/outDebug", "MIMode": "gdb", "miDebuggerPath": "gdb", "setupCommands": [{"description": "Enable pretty-printing for gdb", "text": "-enable-pretty-printing", "ignoreFailures": true}]}]}