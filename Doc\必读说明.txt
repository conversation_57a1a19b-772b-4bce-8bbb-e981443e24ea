/*********************************************************************************************/

【*】 程序简介 
-工程名称：直流无刷电机-速度环控制-位置式PID
-实验平台: 野火STM32 H743 开发板
-MDK版本：5.30
-HAL库版本：1.6.0


【 ！】功能简介：
按下KEY1\KEY2以使能\失能电机,按下KEY3\KEY4可以调整
速度环目标值,以到达加减速的效果.

可以通过上位机<野火多功能调试助手>----PID调试助手,查看现象或进行调试.
在PID调试助手中,打开开发板对应的串口,单击下方启动即可.

注意,部分例程中,上位机设置PID目标值时,未做幅值限制,若出现积分饱和为正常现象.

在电机未停止时重新开启电机,可能出现PID调整不准确的问题,电机会因为惯性保持运行,定时器会捕获不该捕获的脉冲.

部分电机特性不支持低速运行,速度调整过低时会判定为堵转,停止电机运转.
【 ！】实验操作：
接线:
注意接线是有序的:（具体以电机说明的为准）
三相绕组的线序：黄：U		蓝：W			绿：V

编码器的5根线序：
黄:HU	蓝：HW	  绿：HV		黑：H-		红:H+
接线:
注意接线是有序的:
电机驱动板 5V_IN\GND 		<----> 开发板 5V\GND
电机驱动板 U+\V+\W+ 		<----> 开发板 PI5\PI6\PI7
电机驱动板 U-\V-\W- 		<----> 开发板 PH13\PH14\PH15
电机驱动板 SD\GND				<----> 开发板 PE6\GND
电机驱动板 HU\HV\HW\GND	<----> 开发板 PH10\PH11\PH12\GND --> 编码器

下载本程序，复位开发板即可。

【 ！！】注意事项：
无

/*********************************************************************************************/

定时器TIM8输出PWM信号:
三路PWM输出到PI5\PI6\PI7上连接到驱动板上的U+\V+\W+接口.

电机使能引脚:
电机驱动板 SD <----> 开发板 PE6

编码器:
霍尔编码器信号从HU\HV\HW引脚输出,接入开发板PH10\PH11\PH12定时器捕获.
	
/*********************************************************************************************/

【*】 时钟

A.晶振：
-外部高速晶振：25MHz
-RTC晶振：32.768KHz

B.各总线运行时钟：
-系统时钟 = SYCCLK = 480MHz
-AHB4 = 240MHz
-AXI  = 240MHz
-APB1 = 120MHz 
-APB2 = 120MHz 
-APB3 = 120MHz
 
C.浮点运算单元：
  使能


/*********************************************************************************************/

【*】 版本

-程序版本：1.0
-发布日期：2020.7.21

-版本更新说明：首次发布

/*********************************************************************************************/

【*】 联系我们

-野火论坛    :http://www.firebbs.cn
-淘宝店铺    :http://firestm32.taobao.com

/*********************************************************************************************/